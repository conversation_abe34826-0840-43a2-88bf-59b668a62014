"use client";

import { ElementType, useEffect, useRef, useState, createElement, useMemo, useCallback } from "react";
import { gsap } from "gsap";
import { useCurrentFrame, interpolate, useVideoConfig } from "remotion";

interface TextTypeProps {
  className?: string;
  showCursor?: boolean;
  hideCursorWhileTyping?: boolean;
  cursorCharacter?: string | React.ReactNode;
  cursorBlinkDuration?: number;
  cursorClassName?: string;
  text: string | string[];
  as?: ElementType;
  typingSpeed?: number;
  initialDelay?: number;
  pauseDuration?: number;
  deletingSpeed?: number;
  loop?: boolean;
  textColors?: string[];
  variableSpeed?: { min: number; max: number };
  onSentenceComplete?: (sentence: string, index: number) => void;
  startOnVisible?: boolean;
  reverseMode?: boolean;
  // Remotion Frame 支持
  startFrame?: number;
  endFrame?: number;
  duration?: number; // 总动画持续时间（帧数）
  charDuration?: number; // 单个字符动画持续时间（帧数）
  autoAdjustSpeed?: boolean; // 是否根据持续时间自动调整速度
  useFrameBasedAnimation?: boolean; // 是否使用基于帧的动画
}

const TextType = ({
  text,
  as: Component = "div",
  typingSpeed = 50,
  initialDelay = 0,
  pauseDuration = 2000,
  deletingSpeed = 30,
  loop = true,
  className = "",
  showCursor = true,
  hideCursorWhileTyping = false,
  cursorCharacter = "|",
  cursorClassName = "",
  cursorBlinkDuration = 0.5,
  textColors = [],
  variableSpeed,
  onSentenceComplete,
  startOnVisible = false,
  reverseMode = false,
  // Remotion Frame 参数
  startFrame = 0,
  endFrame,
  duration,
  charDuration = 3,
  autoAdjustSpeed = true,
  useFrameBasedAnimation = false,
  ...props
}: TextTypeProps & React.HTMLAttributes<HTMLElement>) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const [displayedText, setDisplayedText] = useState("");
  const [currentCharIndex, setCurrentCharIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(!startOnVisible);
  const cursorRef = useRef<HTMLSpanElement>(null);
  const containerRef = useRef<HTMLElement>(null);

  const textArray = useMemo(() => (Array.isArray(text) ? text : [text]), [text]);

  // 计算基于帧的动画参数
  const getFrameAnimationParams = () => {
    const currentText = textArray[currentTextIndex];
    const textLength = currentText.length;

    // 如果提供了 duration，自动计算其他参数
    if (duration && autoAdjustSpeed) {
      const calculatedEndFrame = startFrame + duration;
      const calculatedCharDuration = Math.max(1, Math.floor(duration / textLength));

      return {
        actualStartFrame: startFrame,
        actualEndFrame: calculatedEndFrame,
        actualCharDuration: calculatedCharDuration,
      };
    }

    // 如果提供了 endFrame
    if (endFrame !== undefined) {
      const totalDuration = endFrame - startFrame;
      const calculatedCharDuration = Math.max(1, Math.floor(totalDuration / textLength));

      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualCharDuration: calculatedCharDuration,
      };
    }

    // 默认值
    const defaultDuration = textLength * charDuration;
    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + defaultDuration,
      actualCharDuration: charDuration,
    };
  };

  const getRandomSpeed = useCallback(() => {
    if (!variableSpeed) return typingSpeed;
    const { min, max } = variableSpeed;
    return Math.random() * (max - min) + min;
  }, [variableSpeed, typingSpeed]);

  const getCurrentTextColor = () => {
    if (textColors.length === 0) return "#ffffff";
    return textColors[currentTextIndex % textColors.length];
  };

  useEffect(() => {
    if (!startOnVisible || !containerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, [startOnVisible]);

  // 基于 frame 的光标闪烁动画
  useEffect(() => {
    if (showCursor && cursorRef.current) {
      // 将 cursorBlinkDuration 从秒转换为帧数，使用实际的 fps
      const blinkDurationInFrames = cursorBlinkDuration * fps;
      const blinkCycle = blinkDurationInFrames * 2; // 完整的闪烁周期（显示+隐藏）

      // 计算当前帧在闪烁周期中的位置
      const cyclePosition = frame % blinkCycle;

      // 使用 interpolate 创建平滑的淡入淡出效果
      const opacity = interpolate(
        cyclePosition,
        [0, blinkDurationInFrames * 0.1, blinkDurationInFrames * 0.9, blinkDurationInFrames, blinkDurationInFrames * 1.1, blinkDurationInFrames * 1.9, blinkCycle],
        [1, 1, 1, 0, 0, 0, 1],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );

      gsap.set(cursorRef.current, { opacity });
    }
  }, [showCursor, cursorBlinkDuration, frame, fps]);

  // 基于帧的动画逻辑
  useEffect(() => {
    if (useFrameBasedAnimation) {
      const { actualStartFrame, actualEndFrame, actualCharDuration } = getFrameAnimationParams();
      const currentText = textArray[currentTextIndex];
      const processedText = reverseMode
        ? currentText.split("").reverse().join("")
        : currentText;

      // 计算当前应该显示的字符数
      const progress = interpolate(
        frame,
        [actualStartFrame, actualEndFrame],
        [0, processedText.length],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );

      const targetCharIndex = Math.floor(progress);
      const newDisplayedText = processedText.slice(0, targetCharIndex);

      if (newDisplayedText !== displayedText) {
        setDisplayedText(newDisplayedText);
        setCurrentCharIndex(targetCharIndex);
      }

      // 检查动画是否完成
      if (frame >= actualEndFrame && onSentenceComplete) {
        onSentenceComplete(currentText, currentTextIndex);
      }
    }
  }, [frame, useFrameBasedAnimation, currentTextIndex, textArray, reverseMode, displayedText, onSentenceComplete, getFrameAnimationParams]);

  // 原有的基于时间的动画逻辑
  useEffect(() => {
    if (useFrameBasedAnimation || !isVisible) return;

    let timeout: NodeJS.Timeout;

    const currentText = textArray[currentTextIndex];
    const processedText = reverseMode
      ? currentText.split("").reverse().join("")
      : currentText;

    const executeTypingAnimation = () => {
      if (isDeleting) {
        if (displayedText === "") {
          setIsDeleting(false);
          if (currentTextIndex === textArray.length - 1 && !loop) {
            return;
          }

          if (onSentenceComplete) {
            onSentenceComplete(textArray[currentTextIndex], currentTextIndex);
          }

          setCurrentTextIndex((prev) => (prev + 1) % textArray.length);
          setCurrentCharIndex(0);
          timeout = setTimeout(() => {}, pauseDuration);
        } else {
          timeout = setTimeout(() => {
            setDisplayedText((prev) => prev.slice(0, -1));
          }, deletingSpeed);
        }
      } else {
        if (currentCharIndex < processedText.length) {
          timeout = setTimeout(
            () => {
              setDisplayedText(
                (prev) => prev + processedText[currentCharIndex]
              );
              setCurrentCharIndex((prev) => prev + 1);
            },
            variableSpeed ? getRandomSpeed() : typingSpeed
          );
        } else if (textArray.length > 1) {
          timeout = setTimeout(() => {
            setIsDeleting(true);
          }, pauseDuration);
        }
      }
    };

    if (currentCharIndex === 0 && !isDeleting && displayedText === "") {
      timeout = setTimeout(executeTypingAnimation, initialDelay);
    } else {
      executeTypingAnimation();
    }

    return () => clearTimeout(timeout);
  }, [
    currentCharIndex,
    displayedText,
    isDeleting,
    typingSpeed,
    deletingSpeed,
    pauseDuration,
    textArray,
    currentTextIndex,
    loop,
    initialDelay,
    isVisible,
    reverseMode,
    variableSpeed,
    onSentenceComplete,
    useFrameBasedAnimation,
  ]);

  const shouldHideCursor =
    hideCursorWhileTyping &&
    (currentCharIndex < textArray[currentTextIndex].length || isDeleting);

  return createElement(
    Component,
    {
      ref: containerRef,
      className: `inline-block whitespace-pre-wrap tracking-tight ${className}`,
      ...props,
    },
    <span className="inline" style={{ color: getCurrentTextColor() }}>
      {displayedText}
    </span>,
    showCursor && (
      <span
        ref={cursorRef}
        className={`ml-1 inline-block opacity-100 ${shouldHideCursor ? "hidden" : ""} ${cursorClassName}`}
      >
        {cursorCharacter}
      </span>
    )
  );
};

export default TextType;
